package com.talkweb.ai.converter.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Simple document chunk representation
 */
class DocumentChunk {
    private final String content;
    private final Map<String, Object> metadata;
    private final float[] embedding;

    public DocumentChunk(String content, Map<String, Object> metadata, float[] embedding) {
        this.content = content;
        this.metadata = metadata;
        this.embedding = embedding;
    }

    public String getContent() { return content; }
    public Map<String, Object> getMetadata() { return metadata; }
    public float[] getEmbedding() { return embedding; }
}

/**
 * Service for generating document embeddings and vector representations
 *
 * This is a mock implementation for demonstration purposes.
 * In a real implementation, this would integrate with actual embedding models.
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Service
@ConditionalOnProperty(name = "ai.enabled", havingValue = "true", matchIfMissing = false)
public class DocumentEmbeddingService {

    private static final Logger logger = LoggerFactory.getLogger(DocumentEmbeddingService.class);
    private static final int MAX_CHUNK_SIZE = 8000; // Maximum characters per chunk

    public DocumentEmbeddingService() {
        // Mock implementation - no dependencies required
    }

    /**
     * Generates embeddings for a document (mock implementation)
     *
     * @param content the document content
     * @param metadata additional metadata for the document
     * @return list of document chunks with mock embeddings
     */
    public List<DocumentChunk> generateEmbeddings(String content, Map<String, Object> metadata) {
        if (content == null || content.trim().isEmpty()) {
            logger.warn("Cannot generate embeddings for empty content");
            return new ArrayList<>();
        }

        try {
            logger.debug("Generating mock embeddings for content of length: {}", content.length());

            // Split content into chunks
            List<String> chunks = splitIntoChunks(content, MAX_CHUNK_SIZE);
            List<DocumentChunk> documents = new ArrayList<>();

            for (int i = 0; i < chunks.size(); i++) {
                String chunk = chunks.get(i);

                // Create document chunk with metadata
                Map<String, Object> chunkMetadata = new java.util.HashMap<>(metadata);
                chunkMetadata.put("chunk_index", i);
                chunkMetadata.put("total_chunks", chunks.size());
                chunkMetadata.put("chunk_size", chunk.length());

                // Generate mock embedding (random vector)
                float[] mockEmbedding = generateMockEmbedding(chunk);

                DocumentChunk document = new DocumentChunk(chunk, chunkMetadata, mockEmbedding);
                documents.add(document);

                logger.debug("Generated mock embedding of size {} for chunk {}", mockEmbedding.length, i);
            }

            logger.info("Generated mock embeddings for {} chunks", documents.size());
            return documents;

        } catch (Exception e) {
            logger.error("Failed to generate embeddings", e);
            return new ArrayList<>();
        }
    }

    /**
     * Generates a mock embedding vector
     */
    private float[] generateMockEmbedding(String text) {
        // Simple mock: create a vector based on text hash
        int hash = text.hashCode();
        float[] embedding = new float[384]; // Common embedding size

        for (int i = 0; i < embedding.length; i++) {
            embedding[i] = (float) Math.sin(hash + i) * 0.1f;
        }

        return embedding;
    }

    /**
     * Generates embeddings asynchronously
     *
     * @param content the document content
     * @param metadata additional metadata for the document
     * @return CompletableFuture with the list of document chunks with embeddings
     */
    public CompletableFuture<List<DocumentChunk>> generateEmbeddingsAsync(String content, Map<String, Object> metadata) {
        return CompletableFuture.supplyAsync(() -> generateEmbeddings(content, metadata));
    }

    /**
     * Generates a single embedding for a text (mock implementation)
     *
     * @param text the text to embed
     * @return the embedding vector
     */
    public float[] generateSingleEmbedding(String text) {
        if (text == null || text.trim().isEmpty()) {
            logger.warn("Cannot generate embedding for empty text");
            return new float[0];
        }

        try {
            logger.debug("Generating single mock embedding for text of length: {}", text.length());

            // Generate mock embedding
            float[] embedding = generateMockEmbedding(text);
            logger.debug("Generated single mock embedding of size: {}", embedding.length);
            return embedding;

        } catch (Exception e) {
            logger.error("Failed to generate single embedding", e);
            return new float[0];
        }
    }

    /**
     * Calculates cosine similarity between two embedding vectors
     * 
     * @param embedding1 first embedding vector
     * @param embedding2 second embedding vector
     * @return cosine similarity score (0-1)
     */
    public double calculateSimilarity(float[] embedding1, float[] embedding2) {
        if (embedding1.length != embedding2.length || embedding1.length == 0) {
            return 0.0;
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < embedding1.length; i++) {
            dotProduct += embedding1[i] * embedding2[i];
            norm1 += embedding1[i] * embedding1[i];
            norm2 += embedding2[i] * embedding2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * Splits content into chunks of specified maximum size
     * 
     * @param content the content to split
     * @param maxSize maximum size per chunk
     * @return list of content chunks
     */
    private List<String> splitIntoChunks(String content, int maxSize) {
        List<String> chunks = new ArrayList<>();
        
        if (content.length() <= maxSize) {
            chunks.add(content);
            return chunks;
        }

        // Split by paragraphs first, then by sentences if needed
        String[] paragraphs = content.split("\n\n");
        StringBuilder currentChunk = new StringBuilder();

        for (String paragraph : paragraphs) {
            if (currentChunk.length() + paragraph.length() + 2 <= maxSize) {
                if (currentChunk.length() > 0) {
                    currentChunk.append("\n\n");
                }
                currentChunk.append(paragraph);
            } else {
                // Save current chunk if not empty
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString());
                    currentChunk = new StringBuilder();
                }

                // If paragraph is too long, split by sentences
                if (paragraph.length() > maxSize) {
                    String[] sentences = paragraph.split("\\. ");
                    for (String sentence : sentences) {
                        if (currentChunk.length() + sentence.length() + 2 <= maxSize) {
                            if (currentChunk.length() > 0) {
                                currentChunk.append(". ");
                            }
                            currentChunk.append(sentence);
                        } else {
                            if (currentChunk.length() > 0) {
                                chunks.add(currentChunk.toString());
                                currentChunk = new StringBuilder();
                            }
                            currentChunk.append(sentence);
                        }
                    }
                } else {
                    currentChunk.append(paragraph);
                }
            }
        }

        // Add the last chunk if not empty
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString());
        }

        return chunks;
    }
}
