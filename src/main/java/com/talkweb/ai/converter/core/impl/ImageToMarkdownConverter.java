package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.config.ImageConversionOptions;
import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.converter.AbstractDocumentConverter;
import com.talkweb.ai.converter.core.converter.ConversionCapabilities;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.ConversionMetadata;
import com.talkweb.ai.converter.model.OcrResult;
import com.talkweb.ai.converter.service.OcrService;
import com.talkweb.ai.converter.util.image.ImagePreprocessor;
import com.talkweb.ai.converter.util.image.TableDetector;
import com.talkweb.ai.converter.util.image.TableExtractor;
import com.talkweb.ai.converter.util.image.TableToMarkdownConverter;
import com.talkweb.ai.converter.util.image.LayoutAnalyzer;
import com.talkweb.ai.converter.util.ai.AiTextPostProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 图像到Markdown转换器
 *
 * 支持多种图像格式的OCR识别和Markdown转换，包括图像预处理、
 * 文本识别、结构化处理和质量控制等功能。
 *
 * 支持的格式：PNG, JPG, JPEG, TIFF, BMP, GIF
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
public class ImageToMarkdownConverter extends AbstractDocumentConverter {

    private static final Logger logger = LoggerFactory.getLogger(ImageToMarkdownConverter.class);

    // 支持的文件扩展名
    private static final Set<String> SUPPORTED_EXTENSIONS = Set.of(
        "png", "jpg", "jpeg", "tiff", "tif", "bmp", "gif"
    );

    private final OcrService ocrService;
    private final ImagePreprocessor imagePreprocessor;
    private final TableDetector tableDetector;
    private final TableExtractor tableExtractor;
    private final TableToMarkdownConverter tableToMarkdownConverter;
    private final LayoutAnalyzer layoutAnalyzer;
    private final AiTextPostProcessor aiTextPostProcessor;
    private ImageConversionOptions defaultOptions;

    @Autowired
    public ImageToMarkdownConverter(OcrService ocrService,
                                  ImagePreprocessor imagePreprocessor,
                                  TableDetector tableDetector,
                                  TableExtractor tableExtractor,
                                  TableToMarkdownConverter tableToMarkdownConverter,
                                  LayoutAnalyzer layoutAnalyzer,
                                  AiTextPostProcessor aiTextPostProcessor) {
        super();
        this.ocrService = ocrService;
        this.imagePreprocessor = imagePreprocessor;
        this.tableDetector = tableDetector;
        this.tableExtractor = tableExtractor;
        this.tableToMarkdownConverter = tableToMarkdownConverter;
        this.layoutAnalyzer = layoutAnalyzer;
        this.aiTextPostProcessor = aiTextPostProcessor;
        this.defaultOptions = ImageConversionOptions.createDefault();
    }

    @Override
    protected ConversionMetadata createMetadata() {
        return ConversionMetadata.builder("Image to Markdown Converter")
                .description("Converts image files to Markdown format using OCR technology")
                .version("1.0")
                .attribute("author", "AI Assistant")
                .attribute("supportedInputFormats", SUPPORTED_EXTENSIONS)
                .attribute("supportedOutputFormats", Set.of("md"))
                .attribute("ocrEnabled", true)
                .attribute("preprocessingEnabled", true)
                .build();
    }

    @Override
    public Set<String> getSupportedExtensions() {
        return SUPPORTED_EXTENSIONS;
    }

    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.METADATA)
                .feature(ConversionCapabilities.Features.IMAGES)
                .capability("ocrSupport", true)
                .capability("multiLanguage", true)
                .capability("imagePreprocessing", true)
                .capability("confidenceScoring", true)
                .capability("asyncProcessing", true)
                .capability("batchProcessing", true)
                .capability("maxFileSize", 50 * 1024 * 1024) // 50MB
                .build();
    }

    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) throws ConversionException {
        if (inputFile == null) {
            throw new ConversionException("Input file cannot be null");
        }

        logger.info("Starting image to Markdown conversion for: {}", inputFile.getName());

        // 验证文件
        if (!validateImageFile(inputFile)) {
            throw new ConversionException("Invalid image file: " + inputFile.getName());
        }

        // 获取转换选项
        ImageConversionOptions options = getConversionOptions(context);

        try {
            // 执行转换
            if (options.isEnableAsyncProcessing()) {
                return convertAsync(inputFile, options).get();
            } else {
                return convertSync(inputFile, options);
            }

        } catch (Exception e) {
            logger.error("Image conversion failed for file: {}", inputFile.getName(), e);
            throw new ConversionException("Image conversion failed: " + e.getMessage(), e);
        }
    }

    /**
     * 同步转换
     */
    private ConversionResult convertSync(File inputFile, ImageConversionOptions options) throws ConversionException {
        try {
            // 1. 读取图像
            BufferedImage originalImage = ImageIO.read(inputFile);
            if (originalImage == null) {
                throw new ConversionException("Failed to read image file: " + inputFile.getName());
            }

            // 2. 图像预处理
            BufferedImage processedImage = originalImage;
            ImagePreprocessor.PreprocessingResult preprocessingResult = null;

            if (options.isPreprocessingEnabled()) {
                preprocessingResult = imagePreprocessor.preprocessImage(originalImage, options.getPreprocessingOptions());
                if (preprocessingResult.isSuccess()) {
                    processedImage = preprocessingResult.getProcessedImage();
                    logger.debug("Image preprocessing completed successfully");
                } else {
                    logger.warn("Image preprocessing failed: {}, using original image", preprocessingResult.getErrorMessage());
                }
            }

            // 3. 表格检测（如果启用高级结构化）
            TableDetector.TableDetectionResult tableDetectionResult = null;
            if (options.getStructureLevel() == ImageConversionOptions.StructureLevel.ADVANCED) {
                tableDetectionResult = tableDetector.detectTables(processedImage);
            }

            // 4. 布局分析（如果启用高级结构化）
            LayoutAnalyzer.LayoutAnalysisResult layoutAnalysisResult = null;
            if (options.getStructureLevel() == ImageConversionOptions.StructureLevel.ADVANCED) {
                layoutAnalysisResult = layoutAnalyzer.analyzeLayout(processedImage);
            }

            // 5. OCR识别
            OcrResult ocrResult;
            if (options.isOcrEnabled()) {
                ocrResult = ocrService.recognizeText(processedImage);
            } else {
                ocrResult = OcrResult.success("", 0.0f);
            }

            // 6. 生成Markdown
            String markdown = generateEnhancedMarkdown(ocrResult, options, inputFile,
                                                     preprocessingResult, tableDetectionResult, layoutAnalysisResult);

            // 5. 创建输出路径
            String parentDir = inputFile.getParent();
            if (parentDir == null) {
                parentDir = ".";
            }
            Path outputPath = Paths.get(parentDir, inputFile.getName() + ".md");

            // 6. 创建转换结果
            ConversionResult.Status status = determineStatus(ocrResult, options);
            ConversionResult result = new ConversionResult(
                status,
                inputFile.getPath(),
                outputPath.toString(),
                markdown
            );

            // 添加元数据
            addMetadataToResult(result, ocrResult, options, preprocessingResult);

            logger.info("Image conversion completed for: {}, status: {}, confidence: {}",
                       inputFile.getName(), status, ocrResult.getConfidence());

            return result;

        } catch (IOException e) {
            throw new ConversionException("Failed to read image file: " + inputFile.getName(), e);
        }
    }

    /**
     * 异步转换
     */
    private CompletableFuture<ConversionResult> convertAsync(File inputFile, ImageConversionOptions options) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return convertSync(inputFile, options);
            } catch (ConversionException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 生成增强的Markdown内容
     */
    private String generateEnhancedMarkdown(OcrResult ocrResult, ImageConversionOptions options,
                                          File inputFile, ImagePreprocessor.PreprocessingResult preprocessingResult,
                                          TableDetector.TableDetectionResult tableDetectionResult,
                                          LayoutAnalyzer.LayoutAnalysisResult layoutAnalysisResult) {
        StringBuilder markdown = new StringBuilder();

        // 添加文档头部
        if (options.isIncludeMetadata()) {
            addDocumentHeader(markdown, inputFile, ocrResult);
        }

        // 处理OCR文本
        String text = ocrResult.getText();
        if (text != null && !text.trim().isEmpty()) {

            // AI文本后处理（如果启用高级结构化）
            if (options.getStructureLevel() == ImageConversionOptions.StructureLevel.ADVANCED) {
                AiTextPostProcessor.AiPostProcessingResult aiResult =
                    aiTextPostProcessor.processText(text, ocrResult.getConfidence() / 100.0);
                if (aiResult.isSuccess()) {
                    text = aiResult.getProcessedText();
                    logger.debug("AI text post-processing applied {} corrections", aiResult.getCorrections().size());
                }
            }

            text = processText(text, options);

            // 根据结构化级别处理文本
            switch (options.getStructureLevel()) {
                case ADVANCED:
                    text = structureTextAdvanced(text, tableDetectionResult, layoutAnalysisResult);
                    break;
                case BASIC:
                    text = structureTextBasic(text);
                    break;
                case NONE:
                default:
                    // 保持原样
                    break;
            }

            markdown.append(text);
        } else {
            markdown.append("*No text content detected in image*\n\n");
        }

        // 添加表格内容（如果检测到表格）
        if (tableDetectionResult != null && tableDetectionResult.isSuccess() && !tableDetectionResult.getTables().isEmpty()) {
            addTableContent(markdown, tableDetectionResult, ocrResult);
        }

        // 添加布局信息（如果启用）
        if (options.isIncludeProcessingInfo() && layoutAnalysisResult != null && layoutAnalysisResult.isSuccess()) {
            addLayoutInfo(markdown, layoutAnalysisResult);
        }

        // 添加处理信息
        if (options.isIncludeProcessingInfo()) {
            addProcessingInfo(markdown, ocrResult, preprocessingResult);
        }

        // 添加置信度信息
        if (options.isIncludeConfidenceInfo()) {
            addConfidenceInfo(markdown, ocrResult);
        }

        return markdown.toString();
    }

    /**
     * 生成Markdown内容（原始方法，保持向后兼容）
     */
    private String generateMarkdown(OcrResult ocrResult, ImageConversionOptions options,
                                  File inputFile, ImagePreprocessor.PreprocessingResult preprocessingResult) {
        StringBuilder markdown = new StringBuilder();

        // 添加文档头部
        if (options.isIncludeMetadata()) {
            addDocumentHeader(markdown, inputFile, ocrResult);
        }

        // 处理OCR文本
        String text = ocrResult.getText();
        if (text != null && !text.trim().isEmpty()) {
            text = processText(text, options);

            // 根据结构化级别处理文本
            switch (options.getStructureLevel()) {
                case ADVANCED:
                    text = structureTextAdvanced(text);
                    break;
                case BASIC:
                    text = structureTextBasic(text);
                    break;
                case NONE:
                default:
                    // 保持原样
                    break;
            }

            markdown.append(text);
        } else {
            markdown.append("*No text content detected in image*\n\n");
        }

        // 添加处理信息
        if (options.isIncludeProcessingInfo()) {
            addProcessingInfo(markdown, ocrResult, preprocessingResult);
        }

        // 添加置信度信息
        if (options.isIncludeConfidenceInfo()) {
            addConfidenceInfo(markdown, ocrResult);
        }

        return markdown.toString();
    }

    /**
     * 添加文档头部
     */
    private void addDocumentHeader(StringBuilder markdown, File inputFile, OcrResult ocrResult) {
        markdown.append("# ").append(getFileNameWithoutExtension(inputFile.getName())).append("\n\n");
        markdown.append("**Source:** ").append(inputFile.getName()).append("\n");
        markdown.append("**Processed:** ").append(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)).append("\n");
        markdown.append("**OCR Confidence:** ").append(String.format("%.1f%%", ocrResult.getConfidence())).append("\n\n");
        markdown.append("---\n\n");
    }

    /**
     * 处理文本内容
     */
    private String processText(String text, ImageConversionOptions options) {
        if (text == null) {
            return "";
        }

        String processed = text;

        // 去除多余空白
        if (options.isRemoveExtraWhitespace()) {
            processed = processed.replaceAll("\\s+", " ");
        }

        // 标准化换行符
        if (options.isNormalizeLineBreaks()) {
            processed = processed.replaceAll("\\r\\n|\\r", "\n");
        }

        // 修剪文本
        if (options.isTrimText()) {
            processed = processed.trim();
        }

        // 限制行长度
        if (options.getMaxLineLength() > 0) {
            processed = wrapText(processed, options.getMaxLineLength());
        }

        return processed;
    }

    /**
     * 基本文本结构化
     */
    private String structureTextBasic(String text) {
        // 简单的段落分离
        return text.replaceAll("\\n\\s*\\n", "\n\n");
    }

    /**
     * 高级文本结构化
     */
    private String structureTextAdvanced(String text) {
        return structureTextAdvanced(text, null, null);
    }

    /**
     * 高级文本结构化（带表格和布局信息）
     */
    private String structureTextAdvanced(String text,
                                       TableDetector.TableDetectionResult tableDetectionResult,
                                       LayoutAnalyzer.LayoutAnalysisResult layoutAnalysisResult) {
        // 基础结构化
        String structured = structureTextBasic(text);

        // 识别可能的标题（全大写或首字母大写的短行）
        structured = structured.replaceAll("(?m)^([A-Z][A-Z\\s]{2,20})$", "## $1");

        // 基于布局分析进行结构化
        if (layoutAnalysisResult != null && layoutAnalysisResult.isSuccess()) {
            structured = applyLayoutStructure(structured, layoutAnalysisResult);
        }

        return structured;
    }

    /**
     * 应用布局结构
     */
    private String applyLayoutStructure(String text, LayoutAnalyzer.LayoutAnalysisResult layoutResult) {
        // 如果是多列布局，尝试重新组织文本
        if (layoutResult.getStructure().isMultiColumn()) {
            // 简化的多列处理
            String[] lines = text.split("\n");
            StringBuilder restructured = new StringBuilder();

            for (String line : lines) {
                if (line.trim().length() > 50) { // 长行可能跨列
                    restructured.append(line).append("\n\n");
                } else {
                    restructured.append(line).append("\n");
                }
            }

            return restructured.toString();
        }

        return text;
    }

    /**
     * 添加表格内容
     */
    private void addTableContent(StringBuilder markdown,
                               TableDetector.TableDetectionResult tableDetectionResult,
                               OcrResult ocrResult) {
        try {
            // 提取表格内容
            BufferedImage image = null; // 这里需要传入原始图像，暂时跳过实际提取

            if (image != null) {
                TableExtractor.TableExtractionResult extractionResult =
                    tableExtractor.extractTables(image, tableDetectionResult.getTables());

                if (extractionResult.isSuccess() && !extractionResult.getTables().isEmpty()) {
                    markdown.append("\n## Detected Tables\n\n");

                    TableToMarkdownConverter.ConversionResult conversionResult =
                        tableToMarkdownConverter.convertTables(extractionResult.getTables());

                    if (conversionResult.isSuccess()) {
                        markdown.append(conversionResult.getMarkdown());
                    } else {
                        markdown.append("*Failed to convert tables to Markdown*\n");
                    }
                }
            } else {
                // 如果没有图像，只显示检测到的表格信息
                markdown.append("\n## Table Detection Results\n\n");
                markdown.append("Detected ").append(tableDetectionResult.getTables().size()).append(" table(s):\n\n");

                for (int i = 0; i < tableDetectionResult.getTables().size(); i++) {
                    TableDetector.TableRegion table = tableDetectionResult.getTables().get(i);
                    markdown.append("- **Table ").append(i + 1).append("**: ")
                           .append(table.getRows()).append("x").append(table.getColumns())
                           .append(" cells, confidence: ").append(String.format("%.1f%%", table.getConfidence() * 100))
                           .append("\n");
                }
                markdown.append("\n");
            }

        } catch (Exception e) {
            logger.warn("Failed to add table content", e);
            markdown.append("\n*Error processing detected tables*\n\n");
        }
    }

    /**
     * 添加布局信息
     */
    private void addLayoutInfo(StringBuilder markdown, LayoutAnalyzer.LayoutAnalysisResult layoutResult) {
        markdown.append("\n---\n\n");
        markdown.append("## Layout Analysis\n\n");

        LayoutAnalyzer.LayoutStructure structure = layoutResult.getStructure();
        markdown.append("- **Layout Type:** ").append(structure.isMultiColumn() ? "Multi-column" : "Single-column").append("\n");
        markdown.append("- **Columns:** ").append(structure.getColumns()).append("\n");
        markdown.append("- **Regions Detected:** ").append(layoutResult.getRegions().size()).append("\n");

        // 显示区域类型统计
        Map<LayoutAnalyzer.RegionType, Long> regionCounts = layoutResult.getRegions().stream()
            .collect(java.util.stream.Collectors.groupingBy(
                LayoutAnalyzer.DocumentRegion::getType,
                java.util.stream.Collectors.counting()));

        if (!regionCounts.isEmpty()) {
            markdown.append("- **Region Types:**\n");
            for (Map.Entry<LayoutAnalyzer.RegionType, Long> entry : regionCounts.entrySet()) {
                markdown.append("  - ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
        }

        markdown.append("\n");
    }

    /**
     * 文本换行
     */
    private String wrapText(String text, int maxLength) {
        StringBuilder wrapped = new StringBuilder();
        String[] lines = text.split("\n");

        for (String line : lines) {
            if (line.length() <= maxLength) {
                wrapped.append(line).append("\n");
            } else {
                String[] words = line.split(" ");
                StringBuilder currentLine = new StringBuilder();

                for (String word : words) {
                    if (currentLine.length() + word.length() + 1 <= maxLength) {
                        if (currentLine.length() > 0) {
                            currentLine.append(" ");
                        }
                        currentLine.append(word);
                    } else {
                        if (currentLine.length() > 0) {
                            wrapped.append(currentLine).append("\n");
                            currentLine = new StringBuilder(word);
                        } else {
                            wrapped.append(word).append("\n");
                        }
                    }
                }

                if (currentLine.length() > 0) {
                    wrapped.append(currentLine).append("\n");
                }
            }
        }

        return wrapped.toString();
    }

    /**
     * 添加处理信息
     */
    private void addProcessingInfo(StringBuilder markdown, OcrResult ocrResult,
                                 ImagePreprocessor.PreprocessingResult preprocessingResult) {
        markdown.append("\n---\n\n");
        markdown.append("## Processing Information\n\n");
        markdown.append("- **Processing Time:** ").append(ocrResult.getProcessingTimeMs()).append("ms\n");
        markdown.append("- **Text Length:** ").append(ocrResult.getTextLength()).append(" characters\n");
        markdown.append("- **Word Count:** ").append(ocrResult.getWordCount()).append("\n");

        if (preprocessingResult != null && preprocessingResult.isSuccess()) {
            markdown.append("- **Preprocessing:** Applied\n");
        }

        markdown.append("\n");
    }

    /**
     * 添加置信度信息
     */
    private void addConfidenceInfo(StringBuilder markdown, OcrResult ocrResult) {
        markdown.append("\n---\n\n");
        markdown.append("## OCR Quality Information\n\n");
        markdown.append("- **Overall Confidence:** ").append(String.format("%.1f%%", ocrResult.getConfidence())).append("\n");
        markdown.append("- **Status:** ").append(ocrResult.getStatus()).append("\n");

        if (ocrResult.getWords() != null && !ocrResult.getWords().isEmpty()) {
            float avgWordConfidence = (float) ocrResult.getWords().stream()
                .mapToDouble(OcrResult.WordResult::getConfidence)
                .average()
                .orElse(0.0);
            markdown.append("- **Average Word Confidence:** ").append(String.format("%.1f%%", avgWordConfidence)).append("\n");
        }

        markdown.append("\n");
    }

    // Helper methods

    private boolean validateImageFile(File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return false;
        }

        return imagePreprocessor.isSupportedFormat(file);
    }

    private ImageConversionOptions getConversionOptions(ConversionContext context) {
        // 从上下文中获取选项，如果没有则使用默认选项
        ImageConversionOptions options = context.getOptions().getOption("imageConversionOptions", defaultOptions);
        return options != null ? options : defaultOptions;
    }

    private ConversionResult.Status determineStatus(OcrResult ocrResult, ImageConversionOptions options) {
        if (!ocrResult.isSuccess()) {
            return ConversionResult.Status.FAILED;
        }

        if (ocrResult.getConfidence() < options.getConfidenceThreshold()) {
            return ConversionResult.Status.PARTIAL_SUCCESS;
        }

        if (!ocrResult.hasText() || ocrResult.getTextLength() < options.getMinTextLength()) {
            return ConversionResult.Status.PARTIAL_SUCCESS;
        }

        return ConversionResult.Status.SUCCESS;
    }

    private void addMetadataToResult(ConversionResult result, OcrResult ocrResult,
                                   ImageConversionOptions options,
                                   ImagePreprocessor.PreprocessingResult preprocessingResult) {
        // ConversionResult doesn't support metadata addition in current implementation
        // Metadata information is already included in the markdown content when options.isIncludeProcessingInfo() is true
        // This method is kept for future extensibility when ConversionResult supports metadata
        logger.debug("Metadata would include: OCR Confidence: {}%, Status: {}, Text Length: {}, Word Count: {}, Processing Time: {}ms",
                    ocrResult.getConfidence(), ocrResult.getStatus(), ocrResult.getTextLength(),
                    ocrResult.getWordCount(), ocrResult.getProcessingTimeMs());
    }

    private String getFileNameWithoutExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }

    /**
     * 设置默认转换选项
     */
    public void setDefaultOptions(ImageConversionOptions options) {
        this.defaultOptions = options != null ? options : ImageConversionOptions.createDefault();
    }

    /**
     * 获取默认转换选项
     */
    public ImageConversionOptions getDefaultOptions() {
        return defaultOptions;
    }
}
