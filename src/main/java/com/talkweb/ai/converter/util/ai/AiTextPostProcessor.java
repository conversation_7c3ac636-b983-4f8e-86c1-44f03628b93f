package com.talkweb.ai.converter.util.ai;

import com.talkweb.ai.converter.service.DocumentSummaryService;
import com.talkweb.ai.converter.service.DocumentEmbeddingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

/**
 * AI文本后处理器
 * 
 * 实现基于AI的文本后处理功能，包括文本纠错、
 * 语义增强和内容优化。
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Component
@ConditionalOnProperty(name = "ai.enabled", havingValue = "true", matchIfMissing = false)
public class AiTextPostProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(AiTextPostProcessor.class);
    
    @Autowired
    private DocumentSummaryService documentSummaryService;
    
    @Autowired
    private DocumentEmbeddingService documentEmbeddingService;
    
    // 常见OCR错误模式
    private static final Map<Pattern, String> COMMON_OCR_ERRORS = new HashMap<>();
    static {
        COMMON_OCR_ERRORS.put(Pattern.compile("\\b0\\b"), "O"); // 数字0误识别为字母O
        COMMON_OCR_ERRORS.put(Pattern.compile("\\bl\\b"), "I"); // 小写l误识别为大写I
        COMMON_OCR_ERRORS.put(Pattern.compile("\\brn\\b"), "m"); // rn误识别为m
        COMMON_OCR_ERRORS.put(Pattern.compile("\\bvv\\b"), "w"); // vv误识别为w
        COMMON_OCR_ERRORS.put(Pattern.compile("\\bcl\\b"), "d"); // cl误识别为d
    }
    
    /**
     * AI后处理配置
     */
    public static class AiPostProcessingConfig {
        private boolean enableErrorCorrection = true;
        private boolean enableSemanticEnhancement = true;
        private boolean enableContentOptimization = true;
        private boolean enableContextualCorrection = true;
        private double confidenceThreshold = 0.7;
        private int maxTextLength = 10000;
        private boolean preserveOriginalStructure = true;
        private boolean enableLanguageDetection = true;
        
        // Getters and setters
        public boolean isEnableErrorCorrection() { return enableErrorCorrection; }
        public void setEnableErrorCorrection(boolean enableErrorCorrection) { this.enableErrorCorrection = enableErrorCorrection; }
        
        public boolean isEnableSemanticEnhancement() { return enableSemanticEnhancement; }
        public void setEnableSemanticEnhancement(boolean enableSemanticEnhancement) { this.enableSemanticEnhancement = enableSemanticEnhancement; }
        
        public boolean isEnableContentOptimization() { return enableContentOptimization; }
        public void setEnableContentOptimization(boolean enableContentOptimization) { this.enableContentOptimization = enableContentOptimization; }
        
        public boolean isEnableContextualCorrection() { return enableContextualCorrection; }
        public void setEnableContextualCorrection(boolean enableContextualCorrection) { this.enableContextualCorrection = enableContextualCorrection; }
        
        public double getConfidenceThreshold() { return confidenceThreshold; }
        public void setConfidenceThreshold(double confidenceThreshold) { this.confidenceThreshold = confidenceThreshold; }
        
        public int getMaxTextLength() { return maxTextLength; }
        public void setMaxTextLength(int maxTextLength) { this.maxTextLength = maxTextLength; }
        
        public boolean isPreserveOriginalStructure() { return preserveOriginalStructure; }
        public void setPreserveOriginalStructure(boolean preserveOriginalStructure) { this.preserveOriginalStructure = preserveOriginalStructure; }
        
        public boolean isEnableLanguageDetection() { return enableLanguageDetection; }
        public void setEnableLanguageDetection(boolean enableLanguageDetection) { this.enableLanguageDetection = enableLanguageDetection; }
    }
    
    /**
     * AI后处理结果
     */
    public static class AiPostProcessingResult {
        private final String processedText;
        private final String originalText;
        private final double confidence;
        private final boolean success;
        private final String errorMessage;
        private final Map<String, Object> metadata;
        private final List<TextCorrection> corrections;
        
        public AiPostProcessingResult(String processedText, String originalText, double confidence, List<TextCorrection> corrections) {
            this.processedText = processedText != null ? processedText : "";
            this.originalText = originalText != null ? originalText : "";
            this.confidence = confidence;
            this.success = true;
            this.errorMessage = null;
            this.metadata = new HashMap<>();
            this.corrections = corrections != null ? new ArrayList<>(corrections) : new ArrayList<>();
        }
        
        public AiPostProcessingResult(String errorMessage) {
            this.processedText = "";
            this.originalText = "";
            this.confidence = 0.0;
            this.success = false;
            this.errorMessage = errorMessage;
            this.metadata = new HashMap<>();
            this.corrections = new ArrayList<>();
        }
        
        // Getters
        public String getProcessedText() { return processedText; }
        public String getOriginalText() { return originalText; }
        public double getConfidence() { return confidence; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
        public List<TextCorrection> getCorrections() { return new ArrayList<>(corrections); }
        
        public void addMetadata(String key, Object value) {
            this.metadata.put(key, value);
        }
    }
    
    /**
     * 文本纠正记录
     */
    public static class TextCorrection {
        private final String original;
        private final String corrected;
        private final int position;
        private final double confidence;
        private final String reason;
        
        public TextCorrection(String original, String corrected, int position, double confidence, String reason) {
            this.original = original;
            this.corrected = corrected;
            this.position = position;
            this.confidence = confidence;
            this.reason = reason;
        }
        
        // Getters
        public String getOriginal() { return original; }
        public String getCorrected() { return corrected; }
        public int getPosition() { return position; }
        public double getConfidence() { return confidence; }
        public String getReason() { return reason; }
    }
    
    /**
     * 处理OCR文本
     * 
     * @param text 原始OCR文本
     * @param confidence OCR置信度
     * @return AI后处理结果
     */
    public AiPostProcessingResult processText(String text, double confidence) {
        return processText(text, confidence, new AiPostProcessingConfig());
    }
    
    /**
     * 处理OCR文本（带配置）
     * 
     * @param text 原始OCR文本
     * @param confidence OCR置信度
     * @param config 处理配置
     * @return AI后处理结果
     */
    public AiPostProcessingResult processText(String text, double confidence, AiPostProcessingConfig config) {
        if (text == null || text.trim().isEmpty()) {
            return new AiPostProcessingResult("Input text is null or empty");
        }
        
        if (config == null) {
            config = new AiPostProcessingConfig();
        }
        
        try {
            logger.debug("Starting AI text post-processing for text length: {}", text.length());
            
            String processedText = text;
            List<TextCorrection> corrections = new ArrayList<>();
            
            // 1. 基础错误纠正
            if (config.isEnableErrorCorrection()) {
                ProcessingStep errorCorrectionResult = performErrorCorrection(processedText, confidence);
                processedText = errorCorrectionResult.text;
                corrections.addAll(errorCorrectionResult.corrections);
            }
            
            // 2. 上下文纠正
            if (config.isEnableContextualCorrection()) {
                ProcessingStep contextualResult = performContextualCorrection(processedText, confidence);
                processedText = contextualResult.text;
                corrections.addAll(contextualResult.corrections);
            }
            
            // 3. 语义增强
            if (config.isEnableSemanticEnhancement()) {
                ProcessingStep semanticResult = performSemanticEnhancement(processedText, confidence);
                processedText = semanticResult.text;
                corrections.addAll(semanticResult.corrections);
            }
            
            // 4. 内容优化
            if (config.isEnableContentOptimization()) {
                ProcessingStep optimizationResult = performContentOptimization(processedText, config);
                processedText = optimizationResult.text;
                corrections.addAll(optimizationResult.corrections);
            }
            
            // 5. 计算最终置信度
            double finalConfidence = calculateFinalConfidence(confidence, corrections);
            
            AiPostProcessingResult result = new AiPostProcessingResult(processedText, text, finalConfidence, corrections);
            result.addMetadata("originalLength", text.length());
            result.addMetadata("processedLength", processedText.length());
            result.addMetadata("correctionsCount", corrections.size());
            result.addMetadata("improvementRatio", calculateImprovementRatio(text, processedText));
            
            logger.debug("AI text post-processing completed. {} corrections applied", corrections.size());
            
            return result;
            
        } catch (Exception e) {
            logger.error("AI text post-processing failed", e);
            return new AiPostProcessingResult("AI text post-processing failed: " + e.getMessage());
        }
    }
    
    /**
     * 处理步骤结果
     */
    private static class ProcessingStep {
        final String text;
        final List<TextCorrection> corrections;
        
        ProcessingStep(String text, List<TextCorrection> corrections) {
            this.text = text;
            this.corrections = corrections != null ? corrections : new ArrayList<>();
        }
    }
    
    /**
     * 执行错误纠正
     */
    private ProcessingStep performErrorCorrection(String text, double confidence) {
        List<TextCorrection> corrections = new ArrayList<>();
        String correctedText = text;
        
        // 应用常见OCR错误纠正
        for (Map.Entry<Pattern, String> entry : COMMON_OCR_ERRORS.entrySet()) {
            Pattern pattern = entry.getKey();
            String replacement = entry.getValue();
            
            String newText = pattern.matcher(correctedText).replaceAll(replacement);
            if (!newText.equals(correctedText)) {
                corrections.add(new TextCorrection(
                    pattern.pattern(), replacement, -1, 0.8, "Common OCR error correction"));
                correctedText = newText;
            }
        }
        
        // 修复明显的字符错误
        correctedText = fixObviousCharacterErrors(correctedText, corrections);
        
        return new ProcessingStep(correctedText, corrections);
    }
    
    /**
     * 修复明显的字符错误
     */
    private String fixObviousCharacterErrors(String text, List<TextCorrection> corrections) {
        String fixed = text;
        
        // 修复连续的相同字符（可能是OCR错误）
        fixed = fixed.replaceAll("(.)\\1{3,}", "$1$1"); // 超过3个连续字符减少为2个
        
        // 修复明显的空格错误
        fixed = fixed.replaceAll("\\s{3,}", "  "); // 多个空格减少为2个
        fixed = fixed.replaceAll("([a-zA-Z])\\s+([a-zA-Z])", "$1$2"); // 单词内部的空格
        
        // 修复标点符号错误
        fixed = fixed.replaceAll("\\s+([,.!?;:])", "$1"); // 标点符号前的空格
        fixed = fixed.replaceAll("([,.!?;:])([a-zA-Z])", "$1 $2"); // 标点符号后缺少空格
        
        if (!fixed.equals(text)) {
            corrections.add(new TextCorrection(
                "various", "fixed", -1, 0.7, "Character error correction"));
        }
        
        return fixed;
    }
    
    /**
     * 执行上下文纠正
     */
    private ProcessingStep performContextualCorrection(String text, double confidence) {
        List<TextCorrection> corrections = new ArrayList<>();
        String correctedText = text;
        
        // 这里可以集成更复杂的上下文分析
        // 目前实现简化版本
        
        // 修复常见的词汇错误
        Map<String, String> contextualCorrections = getContextualCorrections();
        for (Map.Entry<String, String> entry : contextualCorrections.entrySet()) {
            String wrong = entry.getKey();
            String correct = entry.getValue();
            
            if (correctedText.contains(wrong)) {
                correctedText = correctedText.replace(wrong, correct);
                corrections.add(new TextCorrection(
                    wrong, correct, -1, 0.6, "Contextual correction"));
            }
        }
        
        return new ProcessingStep(correctedText, corrections);
    }
    
    /**
     * 获取上下文纠正映射
     */
    private Map<String, String> getContextualCorrections() {
        Map<String, String> corrections = new HashMap<>();
        corrections.put("teh", "the");
        corrections.put("adn", "and");
        corrections.put("taht", "that");
        corrections.put("wiht", "with");
        corrections.put("thier", "their");
        corrections.put("recieve", "receive");
        corrections.put("seperate", "separate");
        corrections.put("occured", "occurred");
        return corrections;
    }

    /**
     * 执行语义增强
     */
    private ProcessingStep performSemanticEnhancement(String text, double confidence) {
        List<TextCorrection> corrections = new ArrayList<>();
        String enhancedText = text;

        try {
            // 使用AI服务进行语义分析和增强
            if (documentSummaryService != null && text.length() > 100) {
                // 生成文本摘要以理解语义
                String summary = documentSummaryService.generateSummary(text, 100);

                // 基于摘要进行语义一致性检查
                if (summary != null && !summary.trim().isEmpty()) {
                    enhancedText = performSemanticConsistencyCheck(text, summary, corrections);
                }
            }

        } catch (Exception e) {
            logger.warn("Semantic enhancement failed, using original text", e);
        }

        return new ProcessingStep(enhancedText, corrections);
    }

    /**
     * 执行语义一致性检查
     */
    private String performSemanticConsistencyCheck(String text, String summary, List<TextCorrection> corrections) {
        // 简化的语义一致性检查
        String[] sentences = text.split("[.!?]+");
        StringBuilder enhancedText = new StringBuilder();

        for (String sentence : sentences) {
            String trimmedSentence = sentence.trim();
            if (!trimmedSentence.isEmpty()) {
                // 检查句子是否与摘要语义一致
                if (isSemanticallyConsistent(trimmedSentence, summary)) {
                    enhancedText.append(trimmedSentence);
                } else {
                    // 尝试修复语义不一致的句子
                    String correctedSentence = attemptSemanticCorrection(trimmedSentence, summary);
                    if (!correctedSentence.equals(trimmedSentence)) {
                        corrections.add(new TextCorrection(
                            trimmedSentence, correctedSentence, -1, 0.5, "Semantic consistency correction"));
                    }
                    enhancedText.append(correctedSentence);
                }
                enhancedText.append(". ");
            }
        }

        return enhancedText.toString().trim();
    }

    /**
     * 检查语义一致性
     */
    private boolean isSemanticallyConsistent(String sentence, String summary) {
        // 简化的语义一致性检查
        // 在实际应用中，可以使用更复杂的NLP技术

        String[] sentenceWords = sentence.toLowerCase().split("\\s+");
        String[] summaryWords = summary.toLowerCase().split("\\s+");

        Set<String> sentenceWordSet = new HashSet<>(Arrays.asList(sentenceWords));
        Set<String> summaryWordSet = new HashSet<>(Arrays.asList(summaryWords));

        // 计算词汇重叠度
        Set<String> intersection = new HashSet<>(sentenceWordSet);
        intersection.retainAll(summaryWordSet);

        double overlapRatio = (double) intersection.size() / Math.max(sentenceWordSet.size(), 1);
        return overlapRatio > 0.1; // 至少10%的词汇重叠
    }

    /**
     * 尝试语义纠正
     */
    private String attemptSemanticCorrection(String sentence, String summary) {
        // 简化的语义纠正
        // 在实际应用中，可以使用AI模型进行更智能的纠正

        String corrected = sentence;

        // 移除明显不相关的词汇
        String[] words = sentence.split("\\s+");
        StringBuilder correctedSentence = new StringBuilder();

        for (String word : words) {
            if (isRelevantWord(word, summary)) {
                correctedSentence.append(word).append(" ");
            }
        }

        corrected = correctedSentence.toString().trim();
        return corrected.isEmpty() ? sentence : corrected;
    }

    /**
     * 检查词汇相关性
     */
    private boolean isRelevantWord(String word, String summary) {
        // 简化的相关性检查
        if (word.length() < 3) return true; // 保留短词

        String lowerWord = word.toLowerCase();
        String lowerSummary = summary.toLowerCase();

        return lowerSummary.contains(lowerWord) ||
               isCommonWord(lowerWord) ||
               isImportantWord(lowerWord);
    }

    /**
     * 检查是否为常用词
     */
    private boolean isCommonWord(String word) {
        Set<String> commonWords = Set.of(
            "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by",
            "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did",
            "will", "would", "could", "should", "may", "might", "can", "must",
            "this", "that", "these", "those", "a", "an", "some", "any", "all", "each", "every"
        );
        return commonWords.contains(word);
    }

    /**
     * 检查是否为重要词汇
     */
    private boolean isImportantWord(String word) {
        // 检查是否为专有名词、数字等重要信息
        return Character.isUpperCase(word.charAt(0)) ||
               word.matches("\\d+") ||
               word.length() > 8; // 长词通常比较重要
    }

    /**
     * 执行内容优化
     */
    private ProcessingStep performContentOptimization(String text, AiPostProcessingConfig config) {
        List<TextCorrection> corrections = new ArrayList<>();
        String optimizedText = text;

        // 长度限制
        if (optimizedText.length() > config.getMaxTextLength()) {
            optimizedText = optimizedText.substring(0, config.getMaxTextLength()) + "...";
            corrections.add(new TextCorrection(
                "original", "truncated", -1, 1.0, "Text length optimization"));
        }

        // 结构优化
        if (config.isPreserveOriginalStructure()) {
            optimizedText = optimizeTextStructure(optimizedText, corrections);
        }

        // 格式化优化
        optimizedText = optimizeTextFormatting(optimizedText, corrections);

        return new ProcessingStep(optimizedText, corrections);
    }

    /**
     * 优化文本结构
     */
    private String optimizeTextStructure(String text, List<TextCorrection> corrections) {
        String optimized = text;

        // 确保段落分隔
        optimized = optimized.replaceAll("([.!?])([A-Z])", "$1\n\n$2");

        // 优化列表格式
        optimized = optimized.replaceAll("([0-9]+\\.)\\s*", "\n$1 ");
        optimized = optimized.replaceAll("([•·-])\\s*", "\n$1 ");

        if (!optimized.equals(text)) {
            corrections.add(new TextCorrection(
                "structure", "optimized", -1, 0.8, "Text structure optimization"));
        }

        return optimized;
    }

    /**
     * 优化文本格式
     */
    private String optimizeTextFormatting(String text, List<TextCorrection> corrections) {
        String formatted = text;

        // 标准化空白字符
        formatted = formatted.replaceAll("\\s+", " ");
        formatted = formatted.replaceAll("\\n\\s+", "\n");
        formatted = formatted.trim();

        // 标准化标点符号
        formatted = formatted.replaceAll("\\s+([,.!?;:])", "$1");
        formatted = formatted.replaceAll("([,.!?;:])([a-zA-Z])", "$1 $2");

        if (!formatted.equals(text)) {
            corrections.add(new TextCorrection(
                "formatting", "standardized", -1, 0.9, "Text formatting optimization"));
        }

        return formatted;
    }

    /**
     * 计算最终置信度
     */
    private double calculateFinalConfidence(double originalConfidence, List<TextCorrection> corrections) {
        double confidence = originalConfidence;

        // 根据纠正数量调整置信度
        for (TextCorrection correction : corrections) {
            confidence = Math.max(confidence, correction.getConfidence());
        }

        // 如果有很多纠正，可能原文质量较差
        if (corrections.size() > 10) {
            confidence *= 0.9;
        } else if (corrections.size() > 5) {
            confidence *= 0.95;
        }

        return Math.min(1.0, confidence);
    }

    /**
     * 计算改进比率
     */
    private double calculateImprovementRatio(String original, String processed) {
        if (original.equals(processed)) {
            return 1.0;
        }

        // 简化的改进度量
        double lengthRatio = (double) processed.length() / Math.max(original.length(), 1);

        // 计算字符变化比例
        int changes = 0;
        int minLength = Math.min(original.length(), processed.length());

        for (int i = 0; i < minLength; i++) {
            if (original.charAt(i) != processed.charAt(i)) {
                changes++;
            }
        }

        double changeRatio = (double) changes / Math.max(original.length(), 1);

        // 改进比率：长度保持合理 + 适度的变化
        return lengthRatio * (1.0 + changeRatio * 0.1);
    }

    /**
     * 获取处理统计信息
     */
    public Map<String, Object> getProcessingStatistics(List<AiPostProcessingResult> results) {
        Map<String, Object> stats = new HashMap<>();

        if (results == null || results.isEmpty()) {
            stats.put("totalProcessed", 0);
            return stats;
        }

        int totalProcessed = results.size();
        int successfulProcessed = 0;
        double avgConfidence = 0.0;
        int totalCorrections = 0;
        double avgImprovementRatio = 0.0;

        for (AiPostProcessingResult result : results) {
            if (result.isSuccess()) {
                successfulProcessed++;
                avgConfidence += result.getConfidence();
                totalCorrections += result.getCorrections().size();

                Object improvementRatio = result.getMetadata().get("improvementRatio");
                if (improvementRatio instanceof Double) {
                    avgImprovementRatio += (Double) improvementRatio;
                }
            }
        }

        stats.put("totalProcessed", totalProcessed);
        stats.put("successfulProcessed", successfulProcessed);
        stats.put("successRate", (double) successfulProcessed / totalProcessed);
        stats.put("avgConfidence", successfulProcessed > 0 ? avgConfidence / successfulProcessed : 0.0);
        stats.put("totalCorrections", totalCorrections);
        stats.put("avgCorrectionsPerText", successfulProcessed > 0 ? (double) totalCorrections / successfulProcessed : 0.0);
        stats.put("avgImprovementRatio", successfulProcessed > 0 ? avgImprovementRatio / successfulProcessed : 1.0);

        return stats;
    }
}
